<?php

use App\Models\ProductionMethod;
use App\Models\Machine;
use App\Models\Material;
use App\Services\EstimateService;
use App\Services\ImpositionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed();
    $this->estimateService = new EstimateService(new ImpositionService());
});

test('calculates estimate with all costs', function () {
    $productionMethod = ProductionMethod::where('slug', 'digital')->first();
    $machine = $productionMethod->machines()->first();
    $material = Material::where('type', 'paper')->first();

    $data = [
        'client_name' => 'Test Client',
        'job_title' => 'Test Job',
        'production_method_id' => $productionMethod->id,
        'machine_id' => $machine->id,
        'material_id' => $material->id,
        'quantity' => 1000,
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ];

    $result = $this->estimateService->calculateEstimate($data);

    expect($result)->toHaveKeys(['imposition', 'costs', 'breakdown']);
    expect($result['costs'])->toHaveKeys([
        'material_cost', 'production_cost', 'setup_cost',
        'total_cost', 'markup_percentage', 'final_price'
    ]);
    expect($result['costs']['final_price'])->toBeGreaterThan($result['costs']['total_cost']);
});

test('creates and saves estimate', function () {
    $productionMethod = ProductionMethod::where('slug', 'digital')->first();
    $machine = $productionMethod->machines()->first();
    $material = Material::where('type', 'paper')->first();

    $data = [
        'client_name' => 'Test Client',
        'job_title' => 'Test Job',
        'production_method_id' => $productionMethod->id,
        'machine_id' => $machine->id,
        'material_id' => $material->id,
        'quantity' => 1000,
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ];

    $estimate = $this->estimateService->createEstimate($data);

    expect($estimate)->toBeInstanceOf(\App\Models\Estimate::class);
    expect($estimate->client_name)->toBe('Test Client');
    expect($estimate->estimate_number)->toStartWith('EST-');
    expect($estimate->status)->toBe('draft');
});

test('gets recommended materials', function () {
    $productionMethod = ProductionMethod::where('slug', 'digital')->first();

    $recommendations = $this->estimateService->getRecommendedMaterials($productionMethod->id);

    expect($recommendations)->toBeArray();
    expect($recommendations)->not->toBeEmpty();

    foreach ($recommendations as $recommendation) {
        expect($recommendation)->toHaveKeys(['material', 'suitability_score', 'notes']);
        expect($recommendation['suitability_score'])->toBeGreaterThan(0);
    }
});
