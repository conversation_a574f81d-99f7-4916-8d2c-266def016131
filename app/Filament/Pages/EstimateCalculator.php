<?php

namespace App\Filament\Pages;

use App\Filament\Resources\EstimateResource;
use App\Models\Machine;
use App\Models\Material;
use App\Models\ProductionMethod;
use App\Services\EstimateService;
use App\Services\ImpositionService;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class EstimateCalculator extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationGroup = 'Estimates';

    protected static ?string $title = 'Estimate Calculator';

    protected static string $view = 'filament.pages.estimate-calculator';

    public ?array $data = [];
    public ?array $calculation = null;
    public ?array $imposition = null;
    public ?array $productionOptions = null;
    public string $optimizationMode = 'optimized';

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Job Details')
                    ->schema([
                        Select::make('customer_id')
                            ->label('Customer')
                            ->options(\App\Models\Customer::active()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->helperText('Select existing customer (optional)'),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('client_name')
                                    ->label('Client Name')
                                    ->required(),
                                TextInput::make('job_title')
                                    ->label('Job Title')
                                    ->required(),
                            ]),
                        Textarea::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ]),

                Section::make('Item Specifications')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('quantity')
                                    ->label('Quantity')
                                    ->numeric()
                                    ->required()
                                    ->default(1000)
                                    ->hidden(fn (callable $get) => $get('has_versions')),
                                TextInput::make('item_width')
                                    ->label('Item Width (mm)')
                                    ->numeric()
                                    ->required()
                                    ->default(210),
                                TextInput::make('item_height')
                                    ->label('Item Height (mm)')
                                    ->numeric()
                                    ->required()
                                    ->default(297),
                            ]),
                        Select::make('material_id')
                            ->label('Material')
                            ->options(Material::active()->get()->mapWithKeys(function ($material) {
                                return [$material->id => $material->display_name];
                            }))
                            ->required()
                            ->reactive()
                            ->helperText('Select the material type - all available sizes will be calculated automatically'),
                        Grid::make(3)
                            ->schema([
                                TextInput::make('bleed')
                                    ->label('Bleed (mm)')
                                    ->numeric()
                                    ->default(3),
                                TextInput::make('gutter')
                                    ->label('Gutter (mm)')
                                    ->numeric()
                                    ->default(5),
                                TextInput::make('markup_percentage')
                                    ->label('Markup (%)')
                                    ->numeric()
                                    ->default(25),
                            ]),
                    ]),

                Section::make('Versions')
                    ->schema([
                        \Filament\Forms\Components\Toggle::make('has_versions')
                            ->label('Multiple Versions')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if (!$state) {
                                    $set('versions', null);
                                }
                            }),

                        \Filament\Forms\Components\Repeater::make('versions')
                            ->label('Version Details')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Version Name')
                                            ->required()
                                            ->placeholder('e.g., Version A, Red, Large'),
                                        TextInput::make('quantity')
                                            ->label('Quantity')
                                            ->numeric()
                                            ->required()
                                            ->default(500),
                                    ]),
                            ])
                            ->visible(fn (callable $get) => $get('has_versions'))
                            ->defaultItems(2)
                            ->addActionLabel('Add Version')
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? 'Version'),

                        Select::make('version_layout_mode')
                            ->label('Version Layout')
                            ->options([
                                'ganged' => 'Ganged (Mixed on same sheets) - Most Efficient',
                                'separate' => 'Separate Sheets per Version',
                            ])
                            ->default('ganged')
                            ->visible(fn (callable $get) => $get('has_versions'))
                            ->helperText('Ganged layout mixes versions on the same sheet for maximum efficiency. Separate sheets keep each version on its own sheets.'),
                    ]),

                Section::make('Production Setup')
                    ->schema([
                        Toggle::make('auto_production_setup')
                            ->label('Automatic Production Setup')
                            ->default(true)
                            ->reactive()
                            ->helperText('Automatically find the best production options, or manually select specific machines'),

                        Select::make('production_method_id')
                            ->label('Production Method')
                            ->options(ProductionMethod::active()->pluck('name', 'id'))
                            ->required()
                            ->reactive()
                            ->visible(fn (callable $get) => !$get('auto_production_setup'))
                            ->afterStateUpdated(fn ($state, callable $set) => $set('machine_id', null)),

                        Select::make('machine_id')
                            ->label('Machine')
                            ->options(function (callable $get) {
                                $methodId = $get('production_method_id');
                                if (!$methodId) return [];
                                return Machine::where('production_method_id', $methodId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->visible(fn (callable $get) => !$get('auto_production_setup'))
                            ->reactive(),
                    ]),



                Actions::make([
                    Action::make('calculate')
                        ->label('Calculate Estimate')
                        ->action('calculateEstimate')
                        ->color('primary')
                        ->size('lg'),
                    Action::make('save')
                        ->label('Save Estimate')
                        ->action('saveEstimate')
                        ->color('success')
                        ->visible(fn () => $this->calculation !== null),
                ])
                ->alignment('center'),
            ])
            ->statePath('data');
    }

    public function calculateEstimate(): void
    {
        $data = $this->form->getState();

        // Validate versions if enabled
        if ($data['has_versions'] ?? false) {
            if (empty($data['versions'])) {
                Notification::make()
                    ->title('Validation Error')
                    ->body('Please add at least one version when multiple versions is enabled.')
                    ->danger()
                    ->send();
                return;
            }

            $totalQuantity = collect($data['versions'])->sum('quantity');
            if ($totalQuantity <= 0) {
                Notification::make()
                    ->title('Validation Error')
                    ->body('Total quantity across all versions must be greater than 0.')
                    ->danger()
                    ->send();
                return;
            }
        }

        try {
            if ($data['auto_production_setup'] ?? true) {
                // Calculate all production options automatically
                $productionOptionService = app(\App\Services\ProductionOptionService::class);
                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($data);

                if (!empty($this->productionOptions)) {
                    // Use the best option for the main calculation
                    $bestOption = $this->productionOptions[0];
                    $this->calculation = $bestOption['calculation'];
                    $this->imposition = $this->calculation['imposition'];

                    Notification::make()
                        ->title('Production options calculated!')
                        ->body(count($this->productionOptions) . ' production options found. Best option selected.')
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('No compatible production options found')
                        ->body('Try adjusting your specifications or switch to manual mode.')
                        ->warning()
                        ->send();
                }
            } else {
                // Manual mode - use selected production method and machine
                $estimateService = app(EstimateService::class);
                $this->calculation = $estimateService->calculateEstimate($data);
                $this->imposition = $this->calculation['imposition'];
                $this->productionOptions = null;

                Notification::make()
                    ->title('Estimate calculated successfully!')
                    ->success()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Calculation Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function selectProductionOption(string $optionId): void
    {
        if (!$this->productionOptions) {
            return;
        }

        $selectedOption = collect($this->productionOptions)->firstWhere('id', $optionId);

        if ($selectedOption) {
            $this->calculation = $selectedOption['calculation'];
            $this->imposition = $this->calculation['imposition'];

            Notification::make()
                ->title('Production option selected!')
                ->body('Using ' . $selectedOption['machine']->name . ' with ' . $selectedOption['material_size']->display_name)
                ->success()
                ->send();
        }
    }

    public function updateVisualization(): void
    {
        if (!$this->calculation) {
            return;
        }

        $data = $this->form->getState();

        // Update the layout mode based on optimization setting
        if ($this->optimizationMode === 'unoptimized') {
            $data['version_layout_mode'] = 'separate';
        } else {
            $data['version_layout_mode'] = 'ganged';
        }

        try {
            if ($data['auto_production_setup'] ?? true) {
                // Recalculate with new optimization mode
                $productionOptionService = app(\App\Services\ProductionOptionService::class);
                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($data);

                if (!empty($this->productionOptions)) {
                    $bestOption = $this->productionOptions[0];
                    $this->calculation = $bestOption['calculation'];
                    $this->imposition = $this->calculation['imposition'];
                }
            } else {
                // Manual mode - recalculate with new layout mode
                $estimateService = app(EstimateService::class);
                $this->calculation = $estimateService->calculateEstimate($data);
                $this->imposition = $this->calculation['imposition'];
            }
        } catch (\Exception $e) {
            // Silently fail to avoid disrupting the UI
        }
    }

    public function saveEstimate(): void
    {
        if (!$this->calculation) {
            Notification::make()
                ->title('No calculation to save')
                ->warning()
                ->send();
            return;
        }

        try {
            $estimateService = app(EstimateService::class);
            $estimate = $estimateService->createEstimate($this->form->getState());

            Notification::make()
                ->title('Estimate saved successfully!')
                ->body("Estimate #{$estimate->estimate_number} created")
                ->success()
                ->send();

            $this->redirect(EstimateResource::getUrl('view', ['record' => $estimate]));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Save Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
