<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MaterialResource\Pages;
use App\Filament\Resources\MaterialResource\RelationManagers;
use App\Models\Material;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MaterialResource extends Resource
{
    protected static ?string $model = Material::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Materials';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->options([
                        'paper' => 'Paper',
                        'vinyl' => 'Vinyl',
                        'fabric' => 'Fabric',
                        'plastic' => 'Plastic',
                        'metal' => 'Metal',
                    ])
                    ->required(),
                Forms\Components\Select::make('finish')
                    ->options([
                        'gloss' => 'Gloss',
                        'matt' => 'Matt',
                        'silk' => 'Silk',
                        'uncoated' => 'Uncoated',
                    ]),
                Forms\Components\TextInput::make('weight')
                    ->numeric()
                    ->suffix('gsm'),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('cost_per_sheet')
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('cost_per_sqm')
                            ->numeric()
                            ->prefix('£'),
                    ]),
                Forms\Components\Toggle::make('is_roll')
                    ->reactive(),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('sheet_width')
                            ->numeric()
                            ->suffix('mm')
                            ->hidden(fn ($get) => $get('is_roll')),
                        Forms\Components\TextInput::make('sheet_height')
                            ->numeric()
                            ->suffix('mm')
                            ->hidden(fn ($get) => $get('is_roll')),
                        Forms\Components\TextInput::make('roll_width')
                            ->numeric()
                            ->suffix('mm')
                            ->visible(fn ($get) => $get('is_roll')),
                        Forms\Components\TextInput::make('cost_per_linear_meter')
                            ->numeric()
                            ->prefix('£')
                            ->visible(fn ($get) => $get('is_roll')),
                    ]),
                Forms\Components\Toggle::make('is_active')
                    ->required()
                    ->default(true),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paper' => 'success',
                        'vinyl' => 'warning',
                        'fabric' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('finish'),
                Tables\Columns\TextColumn::make('weight')
                    ->numeric()
                    ->suffix('gsm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_sheet')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_sqm')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_roll')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'paper' => 'Paper',
                        'vinyl' => 'Vinyl',
                        'fabric' => 'Fabric',
                        'plastic' => 'Plastic',
                        'metal' => 'Metal',
                    ]),
                Tables\Filters\TernaryFilter::make('is_roll'),
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SizesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaterials::route('/'),
            'create' => Pages\CreateMaterial::route('/create'),
            'edit' => Pages\EditMaterial::route('/{record}/edit'),
        ];
    }
}
