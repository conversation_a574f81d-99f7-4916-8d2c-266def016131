<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MaterialSizeResource\Pages;
use App\Filament\Resources\MaterialSizeResource\RelationManagers;
use App\Models\MaterialSize;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MaterialSizeResource extends Resource
{
    protected static ?string $model = MaterialSize::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Materials';

    protected static ?string $navigationLabel = 'Material Sizes';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Material & Size Information')
                    ->schema([
                        Forms\Components\Select::make('material_id')
                            ->label('Material')
                            ->relationship('material', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Size Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., A4, SRA3, Custom 700x1000'),
                                Forms\Components\Toggle::make('is_standard_size')
                                    ->label('Standard Size')
                                    ->helperText('Check if this is a standard paper size (A4, A3, etc.)'),
                            ]),
                    ]),

                Forms\Components\Section::make('Dimensions')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('width_mm')
                                    ->label('Width (mm)')
                                    ->required()
                                    ->numeric()
                                    ->suffix('mm'),
                                Forms\Components\TextInput::make('height_mm')
                                    ->label('Height (mm)')
                                    ->required()
                                    ->numeric()
                                    ->suffix('mm'),
                            ]),
                        Forms\Components\Placeholder::make('calculated_area')
                            ->label('Calculated Area')
                            ->content(function (callable $get) {
                                $width = $get('width_mm');
                                $height = $get('height_mm');
                                if ($width && $height) {
                                    $sqm = ($width * $height) / 1000000;
                                    return number_format($sqm, 4) . ' m²';
                                }
                                return 'Enter dimensions to calculate area';
                            }),
                    ]),

                Forms\Components\Section::make('Pricing')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('cost_per_sheet')
                                    ->label('Cost per Sheet')
                                    ->numeric()
                                    ->prefix('£')
                                    ->step(0.0001),
                                Forms\Components\TextInput::make('cost_per_sqm')
                                    ->label('Cost per m²')
                                    ->numeric()
                                    ->prefix('£')
                                    ->step(0.0001),
                                Forms\Components\TextInput::make('cost_per_linear_meter')
                                    ->label('Cost per Linear Metre')
                                    ->numeric()
                                    ->prefix('£')
                                    ->step(0.0001)
                                    ->helperText('For roll materials only'),
                            ]),
                    ]),

                Forms\Components\Section::make('Availability & Settings')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('minimum_quantity')
                                    ->label('Minimum Quantity')
                                    ->required()
                                    ->numeric()
                                    ->default(1)
                                    ->minValue(1),
                                Forms\Components\Toggle::make('is_available')
                                    ->label('Available')
                                    ->default(true),
                                Forms\Components\Placeholder::make('spacer'),
                            ]),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('material.name')
                    ->label('Material')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Size Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dimensions')
                    ->label('Dimensions')
                    ->getStateUsing(fn ($record) => $record->width_mm . 'mm × ' . $record->height_mm . 'mm'),
                Tables\Columns\TextColumn::make('area_sqm')
                    ->label('Area (m²)')
                    ->getStateUsing(fn ($record) => number_format($record->area_sqm, 4))
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_sheet')
                    ->label('Cost/Sheet')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_sqm')
                    ->label('Cost/m²')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minimum_quantity')
                    ->label('Min Qty')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_standard_size')
                    ->label('Standard')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Available')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('material')
                    ->relationship('material', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\TernaryFilter::make('is_standard_size')
                    ->label('Standard Size'),
                Tables\Filters\TernaryFilter::make('is_available')
                    ->label('Available'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('material.name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaterialSizes::route('/'),
            'create' => Pages\CreateMaterialSize::route('/create'),
            'edit' => Pages\EditMaterialSize::route('/{record}/edit'),
        ];
    }
}
