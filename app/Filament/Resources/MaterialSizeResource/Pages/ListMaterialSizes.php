<?php

namespace App\Filament\Resources\MaterialSizeResource\Pages;

use App\Filament\Resources\MaterialSizeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMaterialSizes extends ListRecords
{
    protected static string $resource = MaterialSizeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
