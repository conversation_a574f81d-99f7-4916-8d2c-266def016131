<?php

namespace App\Filament\Resources\MaterialResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SizesRelationManager extends RelationManager
{
    protected static string $relationship = 'sizes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Size Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., A4, SRA3, Custom 700x1000'),
                        Forms\Components\Toggle::make('is_standard_size')
                            ->label('Standard Size'),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('width_mm')
                            ->label('Width (mm)')
                            ->required()
                            ->numeric()
                            ->suffix('mm'),
                        Forms\Components\TextInput::make('height_mm')
                            ->label('Height (mm)')
                            ->required()
                            ->numeric()
                            ->suffix('mm'),
                    ]),
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('cost_per_sheet')
                            ->label('Cost per Sheet')
                            ->numeric()
                            ->prefix('£')
                            ->step(0.0001),
                        Forms\Components\TextInput::make('cost_per_sqm')
                            ->label('Cost per m²')
                            ->numeric()
                            ->prefix('£')
                            ->step(0.0001),
                        Forms\Components\TextInput::make('cost_per_linear_meter')
                            ->label('Cost per Linear Metre')
                            ->numeric()
                            ->prefix('£')
                            ->step(0.0001),
                    ]),
                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('minimum_quantity')
                            ->label('Minimum Quantity')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->minValue(1),
                        Forms\Components\Toggle::make('is_available')
                            ->label('Available')
                            ->default(true),
                        Forms\Components\Placeholder::make('spacer'),
                    ]),
                Forms\Components\Textarea::make('notes')
                    ->label('Notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Size Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dimensions')
                    ->label('Dimensions')
                    ->getStateUsing(fn ($record) => $record->width_mm . 'mm × ' . $record->height_mm . 'mm'),
                Tables\Columns\TextColumn::make('area_sqm')
                    ->label('Area (m²)')
                    ->getStateUsing(fn ($record) => number_format($record->area_sqm, 4)),
                Tables\Columns\TextColumn::make('cost_per_sheet')
                    ->label('Cost/Sheet')
                    ->money('GBP'),
                Tables\Columns\TextColumn::make('cost_per_sqm')
                    ->label('Cost/m²')
                    ->money('GBP'),
                Tables\Columns\IconColumn::make('is_standard_size')
                    ->label('Standard')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Available')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_standard_size')
                    ->label('Standard Size'),
                Tables\Filters\TernaryFilter::make('is_available')
                    ->label('Available'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }
}
