<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Customer Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Customer Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Company Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        'active' => 'Active',
                                        'inactive' => 'Inactive',
                                        'prospect' => 'Prospect',
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('company_number')
                                    ->label('Company Number')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('vat_number')
                                    ->label('VAT Number')
                                    ->maxLength(255),
                            ]),
                        Forms\Components\TextInput::make('website')
                            ->label('Website')
                            ->url()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Financial Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('credit_limit')
                                    ->label('Credit Limit')
                                    ->numeric()
                                    ->prefix('£')
                                    ->step(0.01),
                                Forms\Components\Select::make('payment_terms')
                                    ->label('Payment Terms')
                                    ->options([
                                        'immediate' => 'Immediate',
                                        '7_days' => '7 Days',
                                        '14_days' => '14 Days',
                                        '30_days' => '30 Days',
                                        '60_days' => '60 Days',
                                        '90_days' => '90 Days',
                                    ])
                                    ->default('30_days')
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Contacts')
                    ->schema([
                        Forms\Components\Repeater::make('contacts')
                            ->relationship()
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('first_name')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('last_name')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('job_title')
                                            ->maxLength(255),
                                    ]),
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('email')
                                            ->email()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('phone')
                                            ->tel()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('mobile')
                                            ->tel()
                                            ->maxLength(255),
                                    ]),
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Toggle::make('is_primary')
                                            ->label('Primary Contact'),
                                        Forms\Components\Toggle::make('receives_quotes')
                                            ->label('Receives Quotes')
                                            ->default(true),
                                        Forms\Components\Toggle::make('receives_invoices')
                                            ->label('Receives Invoices'),
                                    ]),
                                Forms\Components\Textarea::make('notes')
                                    ->rows(2)
                                    ->columnSpanFull(),
                            ])
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string =>
                                ($state['first_name'] ?? '') . ' ' . ($state['last_name'] ?? '') ?: 'Contact'
                            )
                            ->addActionLabel('Add Contact')
                            ->defaultItems(1),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Addresses')
                    ->schema([
                        Forms\Components\Repeater::make('addresses')
                            ->relationship()
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Select::make('type')
                                            ->options([
                                                'billing' => 'Billing',
                                                'delivery' => 'Delivery',
                                                'both' => 'Billing & Delivery',
                                            ])
                                            ->default('delivery')
                                            ->required(),
                                        Forms\Components\TextInput::make('label')
                                            ->placeholder('e.g., Main Office, Warehouse')
                                            ->maxLength(255),
                                        Forms\Components\Toggle::make('is_default')
                                            ->label('Default Address'),
                                    ]),
                                Forms\Components\TextInput::make('company_name')
                                    ->label('Company Name')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('address_line_1')
                                    ->label('Address Line 1')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('address_line_2')
                                    ->label('Address Line 2')
                                    ->maxLength(255),
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('city')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('county')
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('postcode')
                                            ->required()
                                            ->maxLength(255),
                                    ]),
                                Forms\Components\TextInput::make('country')
                                    ->default('United Kingdom')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Textarea::make('delivery_instructions')
                                    ->label('Delivery Instructions')
                                    ->rows(2)
                                    ->columnSpanFull(),
                            ])
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string =>
                                ($state['label'] ?? '') ?: ($state['address_line_1'] ?? '') . ', ' . ($state['city'] ?? '') ?: 'Address'
                            )
                            ->addActionLabel('Add Address')
                            ->defaultItems(1),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Notes')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Additional Notes')
                            ->rows(4)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Company Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'prospect',
                        'danger' => 'inactive',
                    ])
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),
                Tables\Columns\TextColumn::make('primary_contact.full_name')
                    ->label('Primary Contact')
                    ->getStateUsing(fn ($record) => $record->primary_contact?->full_name ?? 'No primary contact'),
                Tables\Columns\TextColumn::make('primary_contact.email')
                    ->label('Email')
                    ->getStateUsing(fn ($record) => $record->primary_contact?->email ?? '-'),
                Tables\Columns\TextColumn::make('payment_terms_label')
                    ->label('Payment Terms'),
                Tables\Columns\TextColumn::make('credit_limit')
                    ->label('Credit Limit')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('estimates_count')
                    ->label('Estimates')
                    ->counts('estimates')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'prospect' => 'Prospect',
                    ]),
                Tables\Filters\SelectFilter::make('payment_terms')
                    ->label('Payment Terms')
                    ->options([
                        'immediate' => 'Immediate',
                        '7_days' => '7 Days',
                        '14_days' => '14 Days',
                        '30_days' => '30 Days',
                        '60_days' => '60 Days',
                        '90_days' => '90 Days',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Customer Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('name')
                                    ->label('Company Name'),
                                Infolists\Components\TextEntry::make('status_label')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'Active' => 'success',
                                        'Prospect' => 'warning',
                                        'Inactive' => 'danger',
                                        default => 'gray',
                                    }),
                            ]),
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('company_number')
                                    ->label('Company Number'),
                                Infolists\Components\TextEntry::make('vat_number')
                                    ->label('VAT Number'),
                            ]),
                        Infolists\Components\TextEntry::make('website')
                            ->label('Website')
                            ->url(fn ($state) => $state),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description'),
                    ]),

                Infolists\Components\Section::make('Financial Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('credit_limit')
                                    ->label('Credit Limit')
                                    ->money('GBP'),
                                Infolists\Components\TextEntry::make('payment_terms_label')
                                    ->label('Payment Terms'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Contacts')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('contacts')
                            ->schema([
                                Infolists\Components\Grid::make(3)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('full_name')
                                            ->label('Name'),
                                        Infolists\Components\TextEntry::make('job_title')
                                            ->label('Job Title'),
                                        Infolists\Components\TextEntry::make('email')
                                            ->label('Email'),
                                    ]),
                                Infolists\Components\Grid::make(3)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('phone')
                                            ->label('Phone'),
                                        Infolists\Components\TextEntry::make('mobile')
                                            ->label('Mobile'),
                                        Infolists\Components\IconEntry::make('is_primary')
                                            ->label('Primary')
                                            ->boolean(),
                                    ]),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Infolists\Components\Section::make('Addresses')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('addresses')
                            ->schema([
                                Infolists\Components\Grid::make(3)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('type_label')
                                            ->label('Type'),
                                        Infolists\Components\TextEntry::make('label')
                                            ->label('Label'),
                                        Infolists\Components\IconEntry::make('is_default')
                                            ->label('Default')
                                            ->boolean(),
                                    ]),
                                Infolists\Components\TextEntry::make('full_address')
                                    ->label('Address')
                                    ->columnSpanFull(),
                                Infolists\Components\TextEntry::make('delivery_instructions')
                                    ->label('Delivery Instructions')
                                    ->columnSpanFull(),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Infolists\Components\Section::make('Notes')
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->label('Additional Notes')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
