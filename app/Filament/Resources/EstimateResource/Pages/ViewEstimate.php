<?php

namespace App\Filament\Resources\EstimateResource\Pages;

use App\Filament\Resources\EstimateResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewEstimate extends ViewRecord
{
    protected static string $resource = EstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('duplicate')
                ->label('Duplicate Estimate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->url(fn () => EstimateResource::getUrl('create', [
                    'client_name' => $this->record->client_name,
                    'job_title' => $this->record->job_title . ' (Copy)',
                    'production_method_id' => $this->record->production_method_id,
                    'machine_id' => $this->record->machine_id,
                    'material_id' => $this->record->material_id,
                    'quantity' => $this->record->quantity,
                    'item_width' => $this->record->item_width,
                    'item_height' => $this->record->item_height,
                    'sheet_width' => $this->record->sheet_width,
                    'sheet_height' => $this->record->sheet_height,
                ])),
        ];
    }
}
