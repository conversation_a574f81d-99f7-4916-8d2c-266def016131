<?php

namespace App\Filament\Resources\EstimateResource\Pages;

use App\Filament\Resources\EstimateResource;
use App\Services\EstimateService;

use Filament\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions as FormActions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditEstimate extends EditRecord
{
    protected static string $resource = EstimateResource::class;

    protected static ?string $title = 'Edit & Recalculate Estimate';

    public ?array $calculation = null;
    public ?array $imposition = null;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Job Details')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('client_name')
                                    ->label('Client Name')
                                    ->required(),
                                TextInput::make('job_title')
                                    ->label('Job Title')
                                    ->required(),
                            ]),
                        Textarea::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ]),

                Section::make('Item Specifications')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('quantity')
                                    ->label('Quantity')
                                    ->numeric()
                                    ->required()
                                    ->hidden(fn ($record) => $record->has_versions),
                                TextInput::make('item_width')
                                    ->label('Item Width (mm)')
                                    ->numeric()
                                    ->required(),
                                TextInput::make('item_height')
                                    ->label('Item Height (mm)')
                                    ->numeric()
                                    ->required(),
                            ]),
                    ]),

                Section::make('Versions')
                    ->schema([
                        \Filament\Forms\Components\Repeater::make('versions')
                            ->label('Version Details')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Version Name')
                                            ->required(),
                                        TextInput::make('quantity')
                                            ->label('Quantity')
                                            ->numeric()
                                            ->required(),
                                    ]),
                            ])
                            ->visible(fn ($record) => $record->has_versions)
                            ->addActionLabel('Add Version')
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? 'Version'),

                        Select::make('version_layout_mode')
                            ->label('Version Layout')
                            ->options([
                                'ganged' => 'Ganged (Mixed on same sheets) - Most Efficient',
                                'separate' => 'Separate Sheets per Version',
                            ])
                            ->default('ganged')
                            ->visible(fn ($record) => $record->has_versions)
                            ->helperText('Ganged layout mixes versions on the same sheet for maximum efficiency.'),
                    ])
                    ->visible(fn ($record) => $record->has_versions),

                Section::make('Production Setup')
                    ->schema([
                        Select::make('production_method_id')
                            ->label('Production Method')
                            ->options(\App\Models\ProductionMethod::active()->pluck('name', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set) => $set('machine_id', null)),

                        Select::make('machine_id')
                            ->label('Machine')
                            ->options(function (callable $get) {
                                $methodId = $get('production_method_id');
                                if (!$methodId) return [];
                                return \App\Models\Machine::where('production_method_id', $methodId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->reactive(),

                        Select::make('material_id')
                            ->label('Material')
                            ->options(\App\Models\Material::active()->get()->mapWithKeys(fn ($material) => [$material->id => $material->display_name]))
                            ->required()
                            ->reactive(),
                    ]),

                Section::make('Sheet/Roll Specifications')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('sheet_width')
                                    ->label('Sheet/Roll Width (mm)')
                                    ->numeric()
                                    ->required(),
                                TextInput::make('sheet_height')
                                    ->label('Sheet/Roll Height (mm)')
                                    ->numeric()
                                    ->required(),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('markup_percentage')
                                    ->label('Markup (%)')
                                    ->numeric()
                                    ->default(25),
                                Select::make('status')
                                    ->options([
                                        'draft' => 'Draft',
                                        'sent' => 'Sent',
                                        'approved' => 'Approved',
                                        'rejected' => 'Rejected',
                                    ])
                                    ->required(),
                            ]),
                    ]),

                FormActions::make([
                    Action::make('recalculate')
                        ->label('Recalculate Estimate')
                        ->action('recalculateEstimate')
                        ->color('primary')
                        ->size('lg'),
                ])
                ->alignment('center'),
            ]);
    }

    public function recalculateEstimate(): void
    {
        $data = $this->form->getState();

        try {
            $estimateService = app(EstimateService::class);
            $this->calculation = $estimateService->calculateEstimate($data);
            $this->imposition = $this->calculation['imposition'];

            // Update the record with new calculations
            $this->record->update([
                'items_per_sheet' => $this->calculation['imposition']['items_per_sheet'],
                'sheets_required' => $this->calculation['imposition']['sheets_required'],
                'material_cost' => $this->calculation['costs']['material_cost'],
                'production_cost' => $this->calculation['costs']['production_cost'],
                'setup_cost' => $this->calculation['costs']['setup_cost'],
                'total_cost' => $this->calculation['costs']['total_cost'],
                'markup_percentage' => $this->calculation['costs']['markup_percentage'],
                'final_price' => $this->calculation['costs']['final_price'],
                'imposition_layout' => $this->calculation['imposition'],
            ]);

            Notification::make()
                ->title('Estimate recalculated successfully!')
                ->body('All costs and imposition data have been updated.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Calculation Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Extract version layout mode from imposition layout if it exists
        if (isset($data['imposition_layout']['layout_mode'])) {
            $data['version_layout_mode'] = $data['imposition_layout']['layout_mode'];
        }

        return $data;
    }
}
