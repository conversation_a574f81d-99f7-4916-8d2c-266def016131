<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Material extends Model
{
    protected $fillable = [
        'name',
        'type',
        'finish',
        'weight',
        'cost_per_sheet',
        'cost_per_sqm',
        'sheet_width',
        'sheet_height',
        'is_roll',
        'roll_width',
        'cost_per_linear_meter',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'cost_per_sheet' => 'decimal:4',
        'cost_per_sqm' => 'decimal:4',
        'cost_per_linear_meter' => 'decimal:4',
        'is_roll' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function sizes(): HasMany
    {
        return $this->hasMany(MaterialSize::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSheets($query)
    {
        return $query->where('is_roll', false);
    }

    public function scopeRolls($query)
    {
        return $query->where('is_roll', true);
    }

    public function calculateSheetCost(int $sheets): float
    {
        if ($this->is_roll) {
            // For roll materials, we need to calculate based on area
            return 0; // This would need sheet dimensions to calculate
        }

        return $sheets * $this->cost_per_sheet;
    }

    public function calculateAreaCost(float $area): float
    {
        return $area * $this->cost_per_sqm;
    }

    public function calculateLinearCost(float $length): float
    {
        if (!$this->is_roll) {
            return 0;
        }

        return $length * $this->cost_per_linear_meter;
    }

    public function getDisplayNameAttribute(): string
    {
        $parts = [$this->name];

        if ($this->weight) {
            $parts[] = $this->weight . 'gsm';
        }

        if ($this->finish) {
            $parts[] = ucfirst($this->finish);
        }

        return implode(' - ', $parts);
    }
}
