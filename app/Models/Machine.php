<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Machine extends Model
{
    protected $fillable = [
        'production_method_id',
        'name',
        'model',
        'hourly_rate',
        'max_sheet_width',
        'max_sheet_height',
        'min_sheet_width',
        'min_sheet_height',
        'sheets_per_hour',
        'waste_percentage',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'waste_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function productionMethod(): BelongsTo
    {
        return $this->belongsTo(ProductionMethod::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function canHandleSheetSize(int $width, int $height): bool
    {
        return $width <= $this->max_sheet_width
            && $height <= $this->max_sheet_height
            && $width >= $this->min_sheet_width
            && $height >= $this->min_sheet_height;
    }

    public function calculateProductionTime(int $sheets): float
    {
        if ($this->sheets_per_hour <= 0) {
            return 0;
        }

        return $sheets / $this->sheets_per_hour;
    }

    public function calculateProductionCost(int $sheets): float
    {
        $hours = $this->calculateProductionTime($sheets);
        return $hours * $this->hourly_rate;
    }
}
