<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class MaterialSize extends Model
{
    use HasFactory;

    protected $fillable = [
        'material_id',
        'name',
        'width_mm',
        'height_mm',
        'cost_per_sheet',
        'cost_per_sqm',
        'cost_per_linear_meter',
        'minimum_quantity',
        'is_standard_size',
        'is_available',
        'notes',
    ];

    protected $casts = [
        'cost_per_sheet' => 'decimal:4',
        'cost_per_sqm' => 'decimal:4',
        'cost_per_linear_meter' => 'decimal:4',
        'is_standard_size' => 'boolean',
        'is_available' => 'boolean',
    ];

    // Relationships
    public function material(): BelongsTo
    {
        return $this->belongsTo(Material::class);
    }

    // Scopes
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('is_available', true);
    }

    public function scopeStandardSizes(Builder $query): Builder
    {
        return $query->where('is_standard_size', true);
    }

    // Accessors
    public function getDimensionsAttribute(): string
    {
        return $this->width_mm . 'mm × ' . $this->height_mm . 'mm';
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->dimensions . ')';
    }

    public function getAreaSqmAttribute(): float
    {
        return ($this->width_mm * $this->height_mm) / 1000000; // Convert mm² to m²
    }

    public function getFullNameAttribute(): string
    {
        return $this->material->name . ' - ' . $this->display_name;
    }

    // Helper methods
    public function calculateCostForQuantity(int $quantity): array
    {
        $costs = [];

        if ($this->cost_per_sheet) {
            $costs['sheet_cost'] = $this->cost_per_sheet * $quantity;
        }

        if ($this->cost_per_sqm) {
            $costs['sqm_cost'] = $this->cost_per_sqm * ($this->area_sqm * $quantity);
        }

        if ($this->cost_per_linear_meter && $this->material->is_roll) {
            $linearMeters = ($this->height_mm / 1000) * $quantity;
            $costs['linear_cost'] = $this->cost_per_linear_meter * $linearMeters;
        }

        return $costs;
    }

    public function isValidForDimensions(int $itemWidth, int $itemHeight): bool
    {
        return $this->width_mm >= $itemWidth && $this->height_mm >= $itemHeight;
    }
}
