<?php

namespace Database\Seeders;

use App\Models\Machine;
use App\Models\ProductionMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MachineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $digitalMethod = ProductionMethod::where('slug', 'digital')->first();
        $lithoMethod = ProductionMethod::where('slug', 'litho')->first();
        $wideFormatMethod = ProductionMethod::where('slug', 'wide-format')->first();

        $machines = [
            // Digital Print Machines
            [
                'production_method_id' => $digitalMethod->id,
                'name' => 'HP Indigo 7900',
                'model' => '7900',
                'hourly_rate' => 85.00,
                'max_sheet_width' => 520,
                'max_sheet_height' => 750,
                'min_sheet_width' => 100,
                'min_sheet_height' => 148,
                'sheets_per_hour' => 4600,
                'waste_percentage' => 3.00,
                'is_active' => true,
                'notes' => 'High-quality digital press for commercial printing',
            ],
            [
                'production_method_id' => $digitalMethod->id,
                'name' => 'Xerox Versant 280',
                'model' => 'Versant 280',
                'hourly_rate' => 65.00,
                'max_sheet_width' => 330,
                'max_sheet_height' => 488,
                'min_sheet_width' => 76,
                'min_sheet_height' => 127,
                'sheets_per_hour' => 2800,
                'waste_percentage' => 4.00,
                'is_active' => true,
                'notes' => 'Versatile digital press for short runs',
            ],
            // Litho Print Machines
            [
                'production_method_id' => $lithoMethod->id,
                'name' => 'Heidelberg XL 106',
                'model' => 'XL 106-8P',
                'hourly_rate' => 120.00,
                'max_sheet_width' => 750,
                'max_sheet_height' => 1050,
                'min_sheet_width' => 210,
                'min_sheet_height' => 297,
                'sheets_per_hour' => 15000,
                'waste_percentage' => 8.00,
                'is_active' => true,
                'notes' => '8-color offset press with perfecting',
            ],
            [
                'production_method_id' => $lithoMethod->id,
                'name' => 'KBA Rapida 75',
                'model' => 'Rapida 75-4',
                'hourly_rate' => 95.00,
                'max_sheet_width' => 530,
                'max_sheet_height' => 750,
                'min_sheet_width' => 210,
                'min_sheet_height' => 297,
                'sheets_per_hour' => 13000,
                'waste_percentage' => 7.00,
                'is_active' => true,
                'notes' => '4-color offset press',
            ],
            // Wide Format Machines
            [
                'production_method_id' => $wideFormatMethod->id,
                'name' => 'HP Latex 570',
                'model' => 'Latex 570',
                'hourly_rate' => 75.00,
                'max_sheet_width' => 1625,
                'max_sheet_height' => null, // Roll fed
                'min_sheet_width' => 210,
                'min_sheet_height' => 297,
                'sheets_per_hour' => 120, // m²/hour instead
                'waste_percentage' => 5.00,
                'is_active' => true,
                'notes' => 'Large format latex printer for indoor/outdoor applications',
            ],
            [
                'production_method_id' => $wideFormatMethod->id,
                'name' => 'Roland VersaUV LEF-300',
                'model' => 'LEF-300',
                'hourly_rate' => 90.00,
                'max_sheet_width' => 770,
                'max_sheet_height' => 330,
                'min_sheet_width' => 50,
                'min_sheet_height' => 50,
                'sheets_per_hour' => 80,
                'waste_percentage' => 3.00,
                'is_active' => true,
                'notes' => 'UV flatbed printer for rigid materials',
            ],
        ];

        foreach ($machines as $machine) {
            Machine::updateOrCreate(
                [
                    'production_method_id' => $machine['production_method_id'],
                    'name' => $machine['name']
                ],
                $machine
            );
        }
    }
}
