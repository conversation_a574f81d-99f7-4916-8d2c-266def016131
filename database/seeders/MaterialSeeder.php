<?php

namespace Database\Seeders;

use App\Models\Material;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $materials = [
            // Paper Materials
            [
                'name' => '130gsm Gloss',
                'type' => 'paper',
                'finish' => 'gloss',
                'weight' => 130,
                'cost_per_sheet' => 0.15,
                'cost_per_sqm' => 2.50,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_roll' => false,
                'is_active' => true,
                'notes' => 'Standard gloss coated paper',
            ],
            [
                'name' => '170gsm Silk',
                'type' => 'paper',
                'finish' => 'silk',
                'weight' => 170,
                'cost_per_sheet' => 0.22,
                'cost_per_sqm' => 3.20,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_roll' => false,
                'is_active' => true,
                'notes' => 'Premium silk finish paper',
            ],
            [
                'name' => '250gsm Matt',
                'type' => 'paper',
                'finish' => 'matt',
                'weight' => 250,
                'cost_per_sheet' => 0.35,
                'cost_per_sqm' => 4.80,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_roll' => false,
                'is_active' => true,
                'notes' => 'Heavy matt coated paper',
            ],
            [
                'name' => '350gsm Uncoated',
                'type' => 'paper',
                'finish' => 'uncoated',
                'weight' => 350,
                'cost_per_sheet' => 0.45,
                'cost_per_sqm' => 6.20,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_roll' => false,
                'is_active' => true,
                'notes' => 'Thick uncoated cardstock',
            ],
            // Roll Materials for Wide Format
            [
                'name' => 'Banner Vinyl',
                'type' => 'vinyl',
                'finish' => 'matt',
                'weight' => 440,
                'cost_per_sqm' => 8.50,
                'is_roll' => true,
                'roll_width' => 1370,
                'cost_per_linear_meter' => 11.65,
                'is_active' => true,
                'notes' => 'PVC banner material for outdoor use',
            ],
            [
                'name' => 'Photo Paper Roll',
                'type' => 'paper',
                'finish' => 'gloss',
                'weight' => 260,
                'cost_per_sqm' => 12.00,
                'is_roll' => true,
                'roll_width' => 1118,
                'cost_per_linear_meter' => 13.42,
                'is_active' => true,
                'notes' => 'High-quality photo paper for posters',
            ],
            [
                'name' => 'Canvas Roll',
                'type' => 'fabric',
                'finish' => 'matt',
                'weight' => 380,
                'cost_per_sqm' => 15.00,
                'is_roll' => true,
                'roll_width' => 1520,
                'cost_per_linear_meter' => 22.80,
                'is_active' => true,
                'notes' => 'Canvas material for art reproductions',
            ],
            [
                'name' => 'Adhesive Vinyl',
                'type' => 'vinyl',
                'finish' => 'gloss',
                'weight' => 80,
                'cost_per_sqm' => 6.50,
                'is_roll' => true,
                'roll_width' => 1370,
                'cost_per_linear_meter' => 8.91,
                'is_active' => true,
                'notes' => 'Self-adhesive vinyl for decals and signs',
            ],
        ];

        foreach ($materials as $material) {
            Material::updateOrCreate(
                ['name' => $material['name']],
                $material
            );
        }
    }
}
