<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Form -->
        <div class="bg-white rounded-lg shadow">
            {{ $this->form }}
        </div>

        <!-- Calculation Results -->
        @if($this->calculation)
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">Updated Calculation Results</h3>
                
                <!-- Version Breakdown -->
                @if(isset($this->imposition['versions']) && count($this->imposition['versions']) > 0)
                    <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium text-blue-900">Versions Breakdown</h4>
                            <span class="text-xs px-2 py-1 bg-blue-200 text-blue-800 rounded">
                                {{ ucfirst($this->imposition['layout_mode'] ?? 'ganged') }} Layout
                            </span>
                        </div>
                        @foreach($this->imposition['version_distribution'] as $version)
                            <div class="flex justify-between text-sm">
                                <span class="text-blue-700">{{ $version['name'] }}:</span>
                                <span class="font-medium text-blue-900">
                                    {{ $version['quantity'] }} items ({{ number_format($version['percentage'], 1) }}%)
                                    @if(isset($version['sheet_start']))
                                        - Sheets {{ $version['sheet_start'] }}-{{ $version['sheet_end'] }}
                                    @endif
                                </span>
                            </div>
                        @endforeach
                        <div class="flex justify-between text-sm font-medium border-t border-blue-300 pt-2 mt-2">
                            <span class="text-blue-700">Total:</span>
                            <span class="text-blue-900">{{ $this->imposition['total_quantity'] }} items</span>
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Imposition Details -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Imposition Details</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Items per sheet:</span>
                                <span class="font-medium">{{ $this->imposition['items_per_sheet'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Sheets required:</span>
                                <span class="font-medium">{{ $this->imposition['sheets_required'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Layout efficiency:</span>
                                <span class="font-medium">{{ number_format($this->imposition['efficiency_percentage'], 1) }}%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cost Breakdown -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-3">Cost Breakdown</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Material cost:</span>
                                <span class="font-medium">£{{ number_format($this->calculation['costs']['material_cost'], 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Production cost:</span>
                                <span class="font-medium">£{{ number_format($this->calculation['costs']['production_cost'], 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Setup cost:</span>
                                <span class="font-medium">£{{ number_format($this->calculation['costs']['setup_cost'], 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total cost:</span>
                                <span class="font-medium">£{{ number_format($this->calculation['costs']['total_cost'], 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between text-lg font-bold border-t pt-3">
                                <span>Final price:</span>
                                <span class="text-green-600">£{{ number_format($this->calculation['costs']['final_price'], 2) }}</span>
                            </div>
                            
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>Cost per item:</span>
                                <span>£{{ number_format($this->calculation['breakdown']['cost_per_item'], 4) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Imposition Visualisation -->
                <div class="mt-6">
                    <h4 class="text-lg font-semibold mb-4 text-gray-900">Layout Visualisation</h4>
                    @include('filament.infolists.imposition-visualization-content', ['imposition' => $this->imposition])
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
