<x-filament-panels::page>
    <div class="space-y-6">
        {{ $this->form }}

        @if($productionOptions && count($productionOptions) > 0)
            <!-- Production Options Table -->
            <div class="bg-white rounded-lg shadow p-6 mt-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Available Production Options</h3>
                    <span class="text-sm px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                        {{ count($productionOptions) }} options found
                    </span>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sheet Size</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items/Sheet</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sheets Required</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost/Item</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Production Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($productionOptions as $index => $option)
                                <tr class="{{ $index === 0 ? 'bg-green-50 border-l-4 border-green-400' : 'hover:bg-gray-50' }}">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $option['machine']->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $option['production_method']->name }}</div>
                                        @if($index === 0)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                                                Best Option
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $option['material_size']->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $option['material_size']->width_mm }}mm × {{ $option['material_size']->height_mm }}mm</div>
                                        @if($option['material_size']->is_standard_size)
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                                Standard
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="flex items-center">
                                            <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $option['efficiency'] }}%"></div>
                                            </div>
                                            <span class="text-xs font-medium">{{ number_format($option['efficiency'], 1) }}%</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $option['items_per_sheet'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $option['sheets_required'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">£{{ number_format($option['final_price'], 2) }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">£{{ number_format($option['cost_per_item'], 4) }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ number_format($option['production_time'], 1) }}h</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        @if($index === 0)
                                            <span class="text-green-600">Selected</span>
                                        @else
                                            <button
                                                wire:click="selectProductionOption('{{ $option['id'] }}')"
                                                class="text-blue-600 hover:text-blue-900"
                                            >
                                                Select
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif

        @if($calculation)
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
                <!-- Calculation Results -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">Calculation Results</h3>

                    <div class="space-y-3">
                        @if(isset($imposition['versions']) && count($imposition['versions']) > 0)
                            <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="font-medium text-blue-900">Versions Breakdown</h4>
                                    <span class="text-xs px-2 py-1 bg-blue-200 text-blue-800 rounded">
                                        {{ ucfirst($imposition['layout_mode'] ?? 'ganged') }} Layout
                                    </span>
                                </div>
                                @foreach($imposition['version_distribution'] as $version)
                                    <div class="flex justify-between text-sm">
                                        <span class="text-blue-700">{{ $version['name'] }}:</span>
                                        <span class="font-medium text-blue-900">
                                            {{ $version['quantity'] }} items ({{ number_format($version['percentage'], 1) }}%)
                                            @if(isset($version['sheet_start']))
                                                - Sheets {{ $version['sheet_start'] }}-{{ $version['sheet_end'] }}
                                            @endif
                                        </span>
                                    </div>
                                @endforeach
                                <div class="flex justify-between text-sm font-medium border-t border-blue-300 pt-2 mt-2">
                                    <span class="text-blue-700">Total:</span>
                                    <span class="text-blue-900">{{ $imposition['total_quantity'] }} items</span>
                                </div>
                            </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600">Items per sheet:</span>
                            <span class="font-medium">{{ $imposition['items_per_sheet'] }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Sheets required:</span>
                            <span class="font-medium">{{ $imposition['sheets_required'] }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Layout efficiency:</span>
                            <span class="font-medium">{{ number_format($imposition['efficiency_percentage'], 1) }}%</span>
                        </div>

                        <hr class="my-4">

                        <div class="flex justify-between">
                            <span class="text-gray-600">Material cost:</span>
                            <span class="font-medium">£{{ number_format($calculation['costs']['material_cost'], 2) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Production cost:</span>
                            <span class="font-medium">£{{ number_format($calculation['costs']['production_cost'], 2) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Setup cost:</span>
                            <span class="font-medium">£{{ number_format($calculation['costs']['setup_cost'], 2) }}</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Total cost:</span>
                            <span class="font-medium">£{{ number_format($calculation['costs']['total_cost'], 2) }}</span>
                        </div>

                        <div class="flex justify-between text-lg font-bold border-t pt-3">
                            <span>Final price:</span>
                            <span class="text-green-600">£{{ number_format($calculation['costs']['final_price'], 2) }}</span>
                        </div>

                        <div class="flex justify-between text-sm text-gray-500">
                            <span>Cost per item:</span>
                            <span>£{{ number_format($calculation['breakdown']['cost_per_item'], 4) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Imposition Visualisation -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Imposition Layout</h3>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm font-medium text-gray-700">Optimise Layout:</label>
                            <select
                                wire:model="optimizationMode"
                                wire:change="updateVisualization"
                                class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="optimized">Yes (Ganged)</option>
                                <option value="unoptimized">No (Separate)</option>
                            </select>
                        </div>
                    </div>

                    <div class="border rounded-lg p-4 bg-gray-50">
                        <div class="relative mx-auto" style="max-width: 400px;">
                            <!-- Sheet representation -->
                            <div class="border-2 border-gray-400 bg-white relative"
                                 style="width: 100%; aspect-ratio: {{ $imposition['layout_data']['sheet_width'] }}/{{ $imposition['layout_data']['sheet_height'] }};">

                                <!-- Items positioned on sheet -->
                                @if(isset($imposition['positions_with_versions']) && count($imposition['positions_with_versions']) > 0)
                                    @foreach($imposition['positions_with_versions'] as $position)
                                        @php
                                            $versionColors = [
                                                0 => ['bg' => 'bg-blue-100', 'border' => 'border-blue-400', 'text' => 'text-blue-800'],
                                                1 => ['bg' => 'bg-green-100', 'border' => 'border-green-400', 'text' => 'text-green-800'],
                                                2 => ['bg' => 'bg-purple-100', 'border' => 'border-purple-400', 'text' => 'text-purple-800'],
                                                3 => ['bg' => 'bg-orange-100', 'border' => 'border-orange-400', 'text' => 'text-orange-800'],
                                                4 => ['bg' => 'bg-red-100', 'border' => 'border-red-400', 'text' => 'text-red-800'],
                                            ];
                                            $versionIndex = array_search($position['version_name'], array_column($imposition['versions'], 'name')) % 5;
                                            $colors = $versionColors[$versionIndex];
                                        @endphp
                                        <div class="absolute {{ $colors['border'] }} {{ $colors['bg'] }} flex items-center justify-center text-xs {{ $colors['text'] }} font-medium"
                                             style="
                                                left: {{ ($position['x'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                                                top: {{ ($position['y'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                                                width: {{ ($position['width'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                                                height: {{ ($position['height'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                                             "
                                             title="{{ $position['version_name'] }} - Item {{ $position['version_item_number'] }}">
                                            {{ substr($position['version_name'], 0, 1) }}{{ $position['version_item_number'] }}
                                        </div>
                                    @endforeach
                                @else
                                    @foreach($imposition['positions'] as $position)
                                        <div class="absolute border border-blue-400 bg-blue-100 flex items-center justify-center text-xs"
                                             style="
                                                left: {{ ($position['x'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                                                top: {{ ($position['y'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                                                width: {{ ($position['width'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                                                height: {{ ($position['height'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                                             ">
                                            {{ $position['item_number'] }}
                                        </div>
                                    @endforeach
                                @endif
                            </div>

                            <!-- Legend -->
                            <div class="mt-4 text-sm text-gray-600">
                                <div class="flex items-center space-x-4 mb-2">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 border border-gray-400 bg-white mr-2"></div>
                                        <span>Sheet ({{ $imposition['layout_data']['sheet_width'] }}×{{ $imposition['layout_data']['sheet_height'] }}mm)</span>
                                    </div>
                                    @if(!isset($imposition['versions']) || count($imposition['versions']) <= 1)
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 border border-blue-400 bg-blue-100 mr-2"></div>
                                            <span>Items ({{ $imposition['layout_data']['item_width'] }}×{{ $imposition['layout_data']['item_height'] }}mm)</span>
                                        </div>
                                    @endif
                                </div>

                                @if(isset($imposition['versions']) && count($imposition['versions']) > 1)
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($imposition['versions'] as $index => $version)
                                            @php
                                                $versionColors = [
                                                    0 => ['bg' => 'bg-blue-100', 'border' => 'border-blue-400'],
                                                    1 => ['bg' => 'bg-green-100', 'border' => 'border-green-400'],
                                                    2 => ['bg' => 'bg-purple-100', 'border' => 'border-purple-400'],
                                                    3 => ['bg' => 'bg-orange-100', 'border' => 'border-orange-400'],
                                                    4 => ['bg' => 'bg-red-100', 'border' => 'border-red-400'],
                                                ];
                                                $colors = $versionColors[$index % 5];
                                            @endphp
                                            <div class="flex items-center">
                                                <div class="w-4 h-4 {{ $colors['border'] }} {{ $colors['bg'] }} mr-1 border"></div>
                                                <span class="text-xs">{{ $version['name'] }} ({{ $version['quantity'] }})</span>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif

                                @if($imposition['rotated'])
                                    <div class="mt-2 text-orange-600">
                                        <span class="font-medium">Note:</span> Items are rotated for optimal fit
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Layout Details -->
                    <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Layout:</span>
                            <span class="font-medium">{{ $imposition['items_horizontal'] }} × {{ $imposition['items_vertical'] }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Bleed:</span>
                            <span class="font-medium">{{ $imposition['layout_data']['bleed'] }}mm</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Gutter:</span>
                            <span class="font-medium">{{ $imposition['layout_data']['gutter'] }}mm</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Waste:</span>
                            <span class="font-medium">{{ number_format(100 - $imposition['efficiency_percentage'], 1) }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
